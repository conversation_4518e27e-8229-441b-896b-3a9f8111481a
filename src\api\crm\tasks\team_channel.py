"""
Handle team channel invitations
currently using Evolution API
"""

from core.models import Order, OrderItem
from services.evolution_api import EvolutionAPIClient, EvolutionAPIError

import logging
from celery import shared_task

logger = logging.getLogger(__name__)


@shared_task
def process_team_channel_invitation(self, order_item_id=None):
    try:
        # Get the order item
        order_item = OrderItem.objects.get(id=order_item_id)
        offering = order_item.offering

        if not offering.team_channel_id or offering.team_channel_id == "":
            logger.info(f"No team channel ID found for offering {offering.name}")
            return

        # Get the order
        order = order_item.order
        user = order.owner
        phone_number = user.phone_number

        evo_client = EvolutionAPIClient()

        self.update_state(
            state="PROGRESS",
            meta={"message": "Creando invitación a Team Channel"},
        )

        data = evo_client.groups.update_participants(
            group_jid=offering.team_channel_id,
            action="add",
            participants=[phone_number],
        )

        if data.get("success") is False:
            order_item.team_channel_invitation_status = (
                OrderItem.TEAM_CHANNEL_INVITATION_ERROR
            )
            order_item.save(update_fields=["team_channel_invitation_status"])

            raise EvolutionAPIError(
                message=f"Error al crear invitación a Team Channel: {data.get('message')}",
                status_code=data.get("status_code"),
            )

    except OrderItem.DoesNotExist:
        logger.error(f"Order item with ID {order_item_id} not found")
        raise Exception(f"Order item with ID {order_item_id} not found")
    except EvolutionAPIError as e:
        logger.error(
            f"Evolution API error while processing team channel invitation: {e}"
        )
        raise
    except Exception as e:
        logger.error(f"Error processing team channel invitation: {e}")
        raise


@shared_task
def process_team_channel_invitation_for_order(self, order_id):
    try:
        order = Order.objects.get(oid=order_id)

        for item in order.items.filter(
            deleted=False,
            process_team_channel_invitation__in=[
                OrderItem.TEAM_CHANNEL_INVITATION_PENDING,
                OrderItem.TEAM_CHANNEL_INVITATION_ERROR,
                OrderItem.TEAM_CHANNEL_INVITATION_FORBIDDEN,
            ],
        ):
            process_team_channel_invitation.delay(str(item.id))

    except Order.DoesNotExist:
        logger.error(f"Order with ID {order_id} not found")
        raise Exception(f"Order with ID {order_id} not found")
    except Exception as e:
        logger.error(
            f"Error processing team channel invitations for order {order_id}: {e}"
        )
        raise
